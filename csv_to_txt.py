#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSV到TXT文件转换器
将CSV文件转换为TXT文件，只保留Key列的数据，并将URL编码的数据解码
将输出保存到指定目录，并将生成的TXT文件合并为一个文件
"""
# grep -vE '\.(png|jpg|gif|jpeg|webp|JPG)$' back.txt >yichang.txt
import os
import csv
import glob
import sys
import urllib.parse
import shutil
import time

def csv_to_txt(csv_file_path, output_dir="output"):
    """
    将CSV文件转换为TXT文件，只保留Key列的数据，并将URL编码的数据解码

    Args:
        csv_file_path: CSV文件路径
        output_dir: 输出目录

    Returns:
        txt_file_path: 生成的TXT文件路径
    """
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 生成TXT文件路径（替换扩展名）
    base_name = os.path.basename(csv_file_path)
    txt_file_name = os.path.splitext(base_name)[0] + '.txt'
    txt_file_path = os.path.join(output_dir, txt_file_name)

    print(f"正在转换: {csv_file_path} -> {txt_file_path}")

    try:
        # 读取CSV文件并写入TXT文件
        with open(csv_file_path, 'r', encoding='utf-8') as csv_file, \
             open(txt_file_path, 'w', encoding='utf-8') as txt_file:

            # 使用csv模块读取CSV文件
            csv_reader = csv.reader(csv_file)

            # 读取第一行以确定Key列的索引
            header = next(csv_reader, None)
            if not header:
                print(f"警告: {csv_file_path} 是空文件或格式不正确")
                return None

            try:
                key_index = header.index('Key')
                print(f"找到Key列，索引为: {key_index}")
            except ValueError:
                print(f"错误: 在 {csv_file_path} 中找不到'Key'列")
                return None

            # 逐行写入TXT文件，只保留Key列，并将URL编码的数据解码
            for row in csv_reader:
                if len(row) > key_index:
                    # 获取Key列的值
                    key_value = row[key_index]
                    # 解码URL编码的数据
                    try:
                        decoded_value = urllib.parse.unquote(key_value)
                        txt_file.write(decoded_value + '\n')
                    except Exception as e:
                        print(f"解码失败: {key_value}, 错误: {str(e)}")
                        # 如果解码失败，则写入原始值
                        txt_file.write(key_value + '\n')

        print(f"转换成功: {txt_file_path}")
        return txt_file_path

    except Exception as e:
        print(f"转换失败: {csv_file_path}, 错误: {str(e)}")
        return None

def merge_txt_files(txt_dir, output_file="merged_output.txt"):
    """
    合并目录下的所有TXT文件为一个文件

    Args:
        txt_dir: TXT文件所在目录
        output_file: 输出文件名

    Returns:
        output_file_path: 合并后的文件路径
    """
    # 获取目录下的所有TXT文件
    txt_files = glob.glob(os.path.join(txt_dir, '*.txt'))

    if not txt_files:
        print(f"{txt_dir} 目录下没有找到TXT文件")
        return None

    print(f"找到 {len(txt_files)} 个TXT文件，开始合并...")

    # 合并所有TXT文件
    output_file_path = output_file
    with open(output_file_path, 'w', encoding='utf-8') as outfile:
        for txt_file in txt_files:
            print(f"正在合并: {txt_file}")
            with open(txt_file, 'r', encoding='utf-8') as infile:
                # 逐行复制内容
                for line in infile:
                    outfile.write(line)

    print(f"合并完成: {output_file_path}")
    return output_file_path

def process_csv_directory(csv_dir, output_dir, merged_file_name):
    """
    处理指定目录下的所有CSV文件

    Args:
        csv_dir: CSV文件目录
        output_dir: 输出目录
        merged_file_name: 合并后的文件名

    Returns:
        successful_conversions: 成功转换的文件数量
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 检查CSV目录是否存在
    if not os.path.exists(csv_dir):
        print(f"{csv_dir} 目录不存在")
        return 0

    # 获取目录下的所有CSV文件
    csv_files = glob.glob(os.path.join(csv_dir, '*.csv'))

    if not csv_files:
        print(f"{csv_dir} 目录下没有找到CSV文件")
        return 0

    print(f"在 {csv_dir} 目录中找到 {len(csv_files)} 个CSV文件")

    # 转换所有CSV文件
    successful_conversions = 0
    for i, csv_file in enumerate(csv_files):
        print(f"处理第 {i+1}/{len(csv_files)} 个文件: {csv_file}")
        if csv_to_txt(csv_file, output_dir):
            successful_conversions += 1

    # 输出转换结果
    print(f"\n转换完成: {successful_conversions}/{len(csv_files)} 个文件成功转换")

    # 合并所有TXT文件
    if successful_conversions > 0:
        merge_txt_files(output_dir, merged_file_name)

    return successful_conversions

def main():
    """主函数"""
    start_time = time.time()

    # 只处理csv3目录
    print("\n=== 开始处理 csv3 目录 ===")
    process_csv_directory("./sec-taoy-yuan-0521", "sec-taoy-new-0521", "sec-taoy-new-0521.txt")

    end_time = time.time()
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main()
