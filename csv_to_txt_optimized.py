#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能CSV到TXT文件转换器
针对96核384G配置优化，支持多进程并行处理
将CSV文件转换为TXT文件，只保留Key列的数据，并将URL编码的数据解码
输出文件名格式：Bucket+日期.txt
"""

import os
import csv
import glob
import sys
import urllib.parse
import time
import re
from multiprocessing import Pool, cpu_count, Manager
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
from queue import Queue

def extract_bucket_and_date_from_key(key_value):
    """
    从Key值中提取Bucket和日期信息
    
    Args:
        key_value: Key列的值
    
    Returns:
        tuple: (bucket, date) 或 (None, None)
    """
    try:
        # 解码URL编码
        decoded_key = urllib.parse.unquote(key_value)
        
        # 提取日期 (格式如: 20210618/102/hls/...)
        parts = decoded_key.split('/')
        date_str = None
        
        for part in parts:
            # 查找8位数字的日期格式
            if len(part) == 8 and part.isdigit():
                # 验证是否为有效日期格式 (YYYYMMDD)
                if part.startswith('20') and int(part[4:6]) <= 12 and int(part[6:8]) <= 31:
                    date_str = part
                    break
        
        # 默认bucket名，可以根据实际情况调整
        bucket_name = "qa-taoy"
        
        return bucket_name, date_str
    except Exception:
        return None, None

def process_csv_chunk(args):
    """
    处理CSV文件的一个数据块
    
    Args:
        args: (csv_file_path, start_line, end_line, output_dir, chunk_id)
    
    Returns:
        tuple: (success, chunk_file_path, bucket, date, processed_lines)
    """
    csv_file_path, start_line, end_line, output_dir, chunk_id = args
    
    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 生成临时文件名
        base_name = os.path.basename(csv_file_path)
        chunk_file_name = f"{os.path.splitext(base_name)[0]}_chunk_{chunk_id}.txt"
        chunk_file_path = os.path.join(output_dir, chunk_file_name)
        
        bucket_name = None
        date_str = None
        processed_lines = 0
        
        with open(csv_file_path, 'r', encoding='utf-8') as csv_file, \
             open(chunk_file_path, 'w', encoding='utf-8') as txt_file:
            
            csv_reader = csv.reader(csv_file)
            
            # 读取标题行
            header = next(csv_reader, None)
            if not header:
                return False, None, None, None, 0
            
            try:
                key_index = header.index('Key')
            except ValueError:
                return False, None, None, None, 0
            
            # 跳到起始行
            current_line = 1  # 已读取标题行
            while current_line < start_line:
                next(csv_reader, None)
                current_line += 1
            
            # 处理指定范围的行
            while current_line <= end_line:
                try:
                    row = next(csv_reader, None)
                    if row is None:
                        break
                    
                    if len(row) > key_index:
                        key_value = row[key_index]
                        
                        # 解码URL编码的数据
                        try:
                            decoded_value = urllib.parse.unquote(key_value)
                            txt_file.write(decoded_value + '\n')
                            
                            # 提取bucket和日期信息（只从第一行提取）
                            if bucket_name is None and date_str is None:
                                bucket_name, date_str = extract_bucket_and_date_from_key(key_value)
                            
                            processed_lines += 1
                        except Exception:
                            # 如果解码失败，写入原始值
                            txt_file.write(key_value + '\n')
                            processed_lines += 1
                    
                    current_line += 1
                except StopIteration:
                    break
        
        return True, chunk_file_path, bucket_name, date_str, processed_lines
    
    except Exception as e:
        print(f"处理块失败: {e}")
        return False, None, None, None, 0

def get_file_line_count(file_path):
    """
    快速获取文件行数
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f) - 1  # 减去标题行
    except Exception:
        return 0

def process_csv_file_parallel(csv_file_path, output_dir, max_workers=None):
    """
    并行处理单个CSV文件
    
    Args:
        csv_file_path: CSV文件路径
        output_dir: 输出目录
        max_workers: 最大工作进程数
    
    Returns:
        tuple: (success, bucket_name, date_str, total_processed_lines)
    """
    if max_workers is None:
        max_workers = min(cpu_count(), 96)  # 最多使用96个核心
    
    print(f"正在处理: {csv_file_path}")
    print(f"使用 {max_workers} 个进程并行处理")
    
    # 获取文件总行数
    total_lines = get_file_line_count(csv_file_path)
    if total_lines == 0:
        print(f"文件为空或无法读取: {csv_file_path}")
        return False, None, None, 0
    
    print(f"文件总行数: {total_lines}")
    
    # 计算每个块的大小
    chunk_size = max(1000, total_lines // max_workers)  # 每个块至少1000行
    
    # 生成任务列表
    tasks = []
    chunk_id = 0
    for start_line in range(1, total_lines + 1, chunk_size):
        end_line = min(start_line + chunk_size - 1, total_lines)
        tasks.append((csv_file_path, start_line, end_line, output_dir, chunk_id))
        chunk_id += 1
    
    print(f"分割为 {len(tasks)} 个块进行处理")
    
    # 并行处理
    chunk_files = []
    bucket_name = None
    date_str = None
    total_processed_lines = 0
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        future_to_task = {executor.submit(process_csv_chunk, task): task for task in tasks}
        
        for future in as_completed(future_to_task):
            success, chunk_file_path, chunk_bucket, chunk_date, processed_lines = future.result()
            
            if success and chunk_file_path:
                chunk_files.append(chunk_file_path)
                total_processed_lines += processed_lines
                
                # 获取bucket和日期信息
                if bucket_name is None and chunk_bucket:
                    bucket_name = chunk_bucket
                if date_str is None and chunk_date:
                    date_str = chunk_date
                
                print(f"完成块处理: {os.path.basename(chunk_file_path)}, 处理行数: {processed_lines}")
    
    # 合并所有块文件
    if chunk_files:
        # 生成最终文件名
        if bucket_name and date_str:
            final_file_name = f"{bucket_name}_{date_str}.txt"
        else:
            base_name = os.path.basename(csv_file_path)
            final_file_name = f"{os.path.splitext(base_name)[0]}.txt"
        
        final_file_path = os.path.join(output_dir, final_file_name)
        
        print(f"合并块文件到: {final_file_path}")
        
        with open(final_file_path, 'w', encoding='utf-8') as final_file:
            for chunk_file in sorted(chunk_files):
                with open(chunk_file, 'r', encoding='utf-8') as chunk:
                    final_file.write(chunk.read())
                # 删除临时块文件
                os.remove(chunk_file)
        
        print(f"处理完成: {final_file_path}, 总处理行数: {total_processed_lines}")
        return True, bucket_name, date_str, total_processed_lines
    
    return False, None, None, 0

def process_csv_directory_optimized(csv_dir, output_dir, max_workers=None):
    """
    优化版本的目录处理函数
    
    Args:
        csv_dir: CSV文件目录
        output_dir: 输出目录
        max_workers: 最大工作进程数
    
    Returns:
        int: 成功处理的文件数量
    """
    if max_workers is None:
        max_workers = min(cpu_count(), 96)
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 检查CSV目录是否存在
    if not os.path.exists(csv_dir):
        print(f"{csv_dir} 目录不存在")
        return 0
    
    # 获取目录下的所有CSV文件
    csv_files = glob.glob(os.path.join(csv_dir, '*.csv'))
    
    if not csv_files:
        print(f"{csv_dir} 目录下没有找到CSV文件")
        return 0
    
    print(f"在 {csv_dir} 目录中找到 {len(csv_files)} 个CSV文件")
    print(f"系统配置: 96核384G，使用 {max_workers} 个进程")
    
    successful_conversions = 0
    total_processed_lines = 0
    
    for i, csv_file in enumerate(csv_files):
        print(f"\n处理第 {i+1}/{len(csv_files)} 个文件: {csv_file}")
        
        success, bucket_name, date_str, processed_lines = process_csv_file_parallel(
            csv_file, output_dir, max_workers
        )
        
        if success:
            successful_conversions += 1
            total_processed_lines += processed_lines
            print(f"文件处理成功，处理行数: {processed_lines}")
        else:
            print(f"文件处理失败: {csv_file}")
    
    print(f"\n处理完成: {successful_conversions}/{len(csv_files)} 个文件成功处理")
    print(f"总处理行数: {total_processed_lines}")
    
    return successful_conversions

def main():
    """主函数"""
    start_time = time.time()
    
    print("高性能CSV转换器 - 96核384G优化版本")
    print("=" * 50)
    
    # 处理csv3目录
    print("\n=== 开始处理 csv3 目录 ===")
    process_csv_directory_optimized("csv3", "output3_optimized", max_workers=96)
    
    end_time = time.time()
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")
    print("处理完成！")

if __name__ == "__main__":
    main()
