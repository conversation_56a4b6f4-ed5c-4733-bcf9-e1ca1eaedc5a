#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文件合并工具
将output3_optimized目录内的所有txt文件合并为5个文件
支持大文件处理和进度显示
"""

import os
import glob
import time
import math
from concurrent.futures import ThreadPoolExecutor

def get_file_size(file_path):
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except Exception:
        return 0

def get_file_line_count(file_path):
    """获取文件行数"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0

def merge_files_to_output(file_list, output_file, file_index, total_files):
    """
    将文件列表合并到输出文件
    
    Args:
        file_list: 要合并的文件列表
        output_file: 输出文件路径
        file_index: 当前输出文件索引
        total_files: 总输出文件数
    """
    print(f"开始创建第 {file_index}/{total_files} 个合并文件: {output_file}")
    print(f"包含 {len(file_list)} 个源文件")
    
    total_lines = 0
    processed_files = 0
    
    start_time = time.time()
    
    with open(output_file, 'w', encoding='utf-8') as outfile:
        for i, input_file in enumerate(file_list):
            print(f"  正在合并 ({i+1}/{len(file_list)}): {os.path.basename(input_file)}")
            
            try:
                with open(input_file, 'r', encoding='utf-8') as infile:
                    file_lines = 0
                    for line in infile:
                        outfile.write(line)
                        file_lines += 1
                        total_lines += 1
                        
                        # 每处理100万行显示一次进度
                        if total_lines % 1000000 == 0:
                            elapsed = time.time() - start_time
                            print(f"    已处理 {total_lines:,} 行，耗时 {elapsed:.1f} 秒")
                
                processed_files += 1
                print(f"    完成: {os.path.basename(input_file)} ({file_lines:,} 行)")
                
            except Exception as e:
                print(f"    错误: 无法处理文件 {input_file}: {e}")
    
    elapsed_time = time.time() - start_time
    print(f"第 {file_index} 个文件创建完成!")
    print(f"  总行数: {total_lines:,}")
    print(f"  处理文件数: {processed_files}/{len(file_list)}")
    print(f"  耗时: {elapsed_time:.2f} 秒")
    print(f"  文件大小: {get_file_size(output_file) / (1024*1024*1024):.2f} GB")
    print("-" * 50)

def distribute_files_by_size(txt_files, num_groups=5):
    """
    按文件大小将文件分配到5个组中，尽量平衡每组的总大小
    
    Args:
        txt_files: txt文件列表
        num_groups: 分组数量
    
    Returns:
        list: 包含5个文件组的列表
    """
    # 获取每个文件的大小
    file_sizes = []
    for file_path in txt_files:
        size = get_file_size(file_path)
        file_sizes.append((file_path, size))
    
    # 按文件大小降序排序
    file_sizes.sort(key=lambda x: x[1], reverse=True)
    
    # 初始化5个组
    groups = [[] for _ in range(num_groups)]
    group_sizes = [0] * num_groups
    
    # 使用贪心算法分配文件到组中
    for file_path, size in file_sizes:
        # 找到当前总大小最小的组
        min_group_index = group_sizes.index(min(group_sizes))
        
        # 将文件分配到该组
        groups[min_group_index].append(file_path)
        group_sizes[min_group_index] += size
    
    # 显示分配结果
    print("文件分配结果:")
    total_size = sum(group_sizes)
    for i, (group, size) in enumerate(zip(groups, group_sizes)):
        size_gb = size / (1024*1024*1024)
        percentage = (size / total_size * 100) if total_size > 0 else 0
        print(f"  组 {i+1}: {len(group)} 个文件, {size_gb:.2f} GB ({percentage:.1f}%)")
    
    return groups

def merge_output3_optimized():
    """
    将output3_optimized目录内的所有txt文件合并为5个文件
    """
    input_dir = "output3_optimized"
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"错误: {input_dir} 目录不存在")
        return
    
    # 获取所有txt文件
    txt_files = glob.glob(os.path.join(input_dir, "*.txt"))
    
    if not txt_files:
        print(f"错误: {input_dir} 目录下没有找到txt文件")
        return
    
    print(f"找到 {len(txt_files)} 个txt文件")
    
    # 计算总大小
    total_size = sum(get_file_size(f) for f in txt_files)
    total_size_gb = total_size / (1024*1024*1024)
    print(f"总文件大小: {total_size_gb:.2f} GB")
    
    # 按大小分配文件到5个组
    file_groups = distribute_files_by_size(txt_files, 5)
    
    # 生成输出文件名（使用Bucket+日期格式）
    output_files = []
    for i in range(5):
        # 从第一个文件中提取bucket和日期信息
        if file_groups[i]:
            first_file = file_groups[i][0]
            base_name = os.path.basename(first_file)
            
            # 尝试提取bucket和日期
            if "_" in base_name:
                parts = base_name.split("_")
                if len(parts) >= 2:
                    bucket = parts[0]
                    date_part = parts[1].split(".")[0]  # 移除.txt扩展名
                    output_file = f"{bucket}_{date_part}_part{i+1}.txt"
                else:
                    output_file = f"merged_part{i+1}.txt"
            else:
                output_file = f"merged_part{i+1}.txt"
        else:
            output_file = f"merged_part{i+1}.txt"
        
        output_files.append(output_file)
    
    print(f"\n将创建以下5个合并文件:")
    for i, output_file in enumerate(output_files):
        print(f"  {i+1}. {output_file}")
    
    # 开始合并处理
    print(f"\n开始合并处理...")
    start_time = time.time()
    
    # 串行处理每个输出文件（避免磁盘I/O冲突）
    for i, (file_group, output_file) in enumerate(zip(file_groups, output_files)):
        if file_group:  # 只处理非空组
            merge_files_to_output(file_group, output_file, i+1, 5)
    
    total_time = time.time() - start_time
    
    # 显示最终结果
    print("\n" + "=" * 60)
    print("合并完成!")
    print(f"总耗时: {total_time:.2f} 秒")
    print("\n生成的文件:")
    
    total_output_size = 0
    for i, output_file in enumerate(output_files):
        if os.path.exists(output_file):
            size = get_file_size(output_file)
            size_gb = size / (1024*1024*1024)
            line_count = get_file_line_count(output_file)
            total_output_size += size
            print(f"  {i+1}. {output_file}")
            print(f"     大小: {size_gb:.2f} GB")
            print(f"     行数: {line_count:,}")
    
    total_output_gb = total_output_size / (1024*1024*1024)
    print(f"\n总输出大小: {total_output_gb:.2f} GB")
    
    # 验证数据完整性
    if abs(total_size - total_output_size) < 1024:  # 允许1KB的差异
        print("✅ 数据完整性验证通过")
    else:
        print("⚠️  数据大小不匹配，请检查")
        print(f"   输入总大小: {total_size_gb:.2f} GB")
        print(f"   输出总大小: {total_output_gb:.2f} GB")

def main():
    """主函数"""
    print("文件合并工具 - 将output3_optimized合并为5个文件")
    print("=" * 60)
    
    merge_output3_optimized()

if __name__ == "__main__":
    main()
